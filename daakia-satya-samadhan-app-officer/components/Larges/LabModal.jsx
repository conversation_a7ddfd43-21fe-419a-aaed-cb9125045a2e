import React, { useState, useEffect, useCallback, useRef } from "react";
import { View, Text, TouchableOpacity, Modal, StyleSheet, ActivityIndicator, Animated } from "react-native";
import { Colors } from "../../constants/colors";
import { GestureHandlerRootView, PanGestureHandler } from 'react-native-gesture-handler';
import * as Haptics from 'expo-haptics';
import { apiService } from '../../services/api';

// Enhanced error logger
const logError = (context, error) => {
  const errorMessage = `ERROR [${context}]: ${error.message || error}`;
  
  console.log('\n' + '='.repeat(80));
  console.log(errorMessage);
  console.log('Stack:', error.stack || 'No stack trace available');
  
  if (error.response) {
    console.log('Response data:', error.response.data);
    console.log('Response status:', error.response.status);
  }
  
  if (error.request) {
    console.log('Request:', error.request);
  }
  
  if (error.config) {
    console.log('Request URL:', error.config.url);
    console.log('Request method:', error.config.method);
  }
  
  console.log('='.repeat(80) + '\n');
  
  return errorMessage;
};

const LabSelectionModal = ({
  isVisible,
  onClose,
  onConfirmSelection,
}) => {
  const [labs, setLabs] = useState([]);
  const [departments, setDepartments] = useState([]);
  const [selectedLab, setSelectedLab] = useState(null);
  const [selectedDepartments, setSelectedDepartments] = useState([]);
  const [completedLabDepartments, setCompletedLabDepartments] = useState({}); // {labId: [departmentIds]}
  const [errors, setErrors] = useState({});
  const [currentStep, setCurrentStep] = useState('lab');
  const [isLoading, setIsLoading] = useState(false);
  
  // Drag and drop state
  const [isDragging, setIsDragging] = useState(false);
  const [draggedItem, setDraggedItem] = useState(null);
  const [draggedItemIndex, setDraggedItemIndex] = useState(null);
  
  // Animation values
  const dragY = useRef(new Animated.Value(0)).current;
  const itemRefs = useRef({});

  const fetchLabs = useCallback(async () => {
    setIsLoading(true);
    console.log('🔄 [LabModal] Fetching labs using API service...');

    try {
      const response = await apiService.fetchLabs();
      console.log('✅ [LabModal] Labs API Response:');
      console.log('📋 Response Data:', JSON.stringify(response, null, 2));

      if (response.status === "success") {
        console.log(`📊 [LabModal] Successfully loaded ${response.data.length} labs`);
        setLabs(response.data);
      } else {
        throw new Error(`API returned error: ${response.message || 'Unknown error'}`);
      }
    } catch (error) {
      console.log('❌ [LabModal] Error fetching labs:', error);
      const errorMsg = logError('Fetching Labs', error);
      setErrors(prev => ({ ...prev, labsFetch: errorMsg }));
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Fetch departments for the selected lab - using useCallback
  const fetchDepartments = useCallback(async (labId) => {
    if (!labId) {
      const errorMsg = logError('Fetch Departments', 'No lab ID provided');
      setErrors(prev => ({ ...prev, departmentsFetch: errorMsg }));
      return;
    }

    setIsLoading(true);
    console.log(`🔄 [LabModal] Fetching departments for lab ID: ${labId}`);

    try {
      const response = await apiService.fetchDepartments(labId);
      console.log('✅ [LabModal] Departments API Response:');
      console.log('📋 Response Data:', JSON.stringify(response, null, 2));

      if (response.status === "success") {
        console.log(`📊 [LabModal] Successfully loaded ${response.data.length} departments for lab ${labId}`);
        setDepartments(response.data);
        setCurrentStep('department');
      } else {
        throw new Error(`API returned error: ${response.message || 'Unknown error'}`);
      }
    } catch (error) {
      console.log('❌ [LabModal] Error fetching departments:', error);
      const errorMsg = logError('Fetching Departments', error);
      setErrors(prev => ({ ...prev, departmentsFetch: errorMsg }));
      // Even in case of error, switch to department step to show error
      setCurrentStep('department');
    } finally {
      setIsLoading(false);
    }
  }, []);
  
  // Prefetch labs when the modal becomes visible
  useEffect(() => {
    if (isVisible) {
      setCurrentStep('lab');
      fetchLabs();
    } else {
      // Reset state when modal is closed
      setSelectedLab(null);
      setSelectedDepartments([]);
      setCompletedLabDepartments({});
      setErrors({});
      setIsDragging(false);
      setDraggedItem(null);
    }
  }, [isVisible, fetchLabs]);

  // Handle lab selection
  const handleLabSelect = (lab) => {
    if (isDragging) return;
    console.log(`🏢 [LabModal] Lab selected: "${lab.name}" (ID: ${lab._id})`);
    setSelectedLab(lab);

    // Load existing selected departments for this lab
    const existingDepartments = completedLabDepartments[lab._id] || [];
    console.log(`📋 [LabModal] Loading existing departments for this lab: ${existingDepartments.length} departments`);
    setSelectedDepartments(existingDepartments);

    fetchDepartments(lab._id);
  };

  // Handle department selection (multi-select)
  const handleDepartmentSelect = (department) => {
    if (isDragging) return;

    setSelectedDepartments(prev => {
      const isSelected = prev.includes(department._id);
      if (isSelected) {
        // Remove if already selected
        console.log(`➖ [LabModal] Department deselected: "${department.name}" (ID: ${department._id})`);
        const newSelection = prev.filter(id => id !== department._id);
        console.log(`📊 [LabModal] Current department count: ${newSelection.length}`);
        return newSelection;
      } else {
        // Add if not selected
        console.log(`➕ [LabModal] Department selected: "${department.name}" (ID: ${department._id})`);
        const newSelection = [...prev, department._id];
        console.log(`📊 [LabModal] Current department count: ${newSelection.length}`);
        return newSelection;
      }
    });
  };

  // Handle confirmation - Save departments for current lab and go back to lab selection
  const handleConfirm = () => {
    if (!selectedLab || selectedDepartments.length === 0) {
      const errorMsg = logError('Confirmation', 'No lab or departments selected');
      setErrors(prev => ({ ...prev, confirmation: errorMsg }));
      return;
    }

    console.log(`✅ [LabModal] Confirming selection for lab: "${selectedLab.name}"`);
    console.log(`📋 [LabModal] Selected departments: ${selectedDepartments.length} departments`);
    console.log(`📊 [LabModal] Department IDs:`, selectedDepartments);

    // Save the lab-department selection
    setCompletedLabDepartments(prev => {
      const updated = {
        ...prev,
        [selectedLab._id]: selectedDepartments
      };
      console.log(`💾 [LabModal] Updated completed selections:`, updated);
      return updated;
    });

    // Go back to lab selection
    console.log(`🔙 [LabModal] Returning to lab selection screen`);
    setCurrentStep('lab');
    setSelectedLab(null);
    setSelectedDepartments([]);
  };
  
  // Go back to lab selection
  const goBackToLabs = () => {
    setCurrentStep('lab');
    setSelectedLab(null);
    setSelectedDepartments([]);
    setIsDragging(false);
  };

  // Handle final submission
  const handleFinalSubmit = () => {
    if (Object.keys(completedLabDepartments).length === 0) {
      const errorMsg = logError('Final Submit', 'No lab-department selections made');
      setErrors(prev => ({ ...prev, finalSubmit: errorMsg }));
      return;
    }

    // Convert to API format with priority
    const labDepartmentArray = [];
    let globalPriority = 1;

    Object.entries(completedLabDepartments).forEach(([labId, departmentIds]) => {
      departmentIds.forEach((departmentId) => {
        labDepartmentArray.push({
          labId: labId,
          labDepartmentId: departmentId,
          priority: globalPriority++
        });
      });
    });

    const apiPayload = {
      lab_department: labDepartmentArray
    };

    console.log('🚀 [LabModal] Final Submission - API Payload:');
    console.log('📤 Payload Structure:', JSON.stringify(apiPayload, null, 2));
    console.log('📊 Summary:');
    console.log(`   • Total Labs: ${Object.keys(completedLabDepartments).length}`);
    console.log(`   • Total Departments: ${labDepartmentArray.length}`);
    console.log(`   • Priority Range: 1-${labDepartmentArray.length}`);

    onConfirmSelection(apiPayload);
    onClose();
  };

  // Check if any labs have been completed
  const hasCompletedSelections = Object.keys(completedLabDepartments).length > 0;

  // Start dragging
  const onDragStart = (item, index) => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    setDraggedItem(item);
    setDraggedItemIndex(index);
    setIsDragging(true);
  };

  // Handle pan gesture
  const onPanGestureEvent = (event) => {    
    dragY.setValue(event.nativeEvent.translationY);
    
    // Calculate new position
    const currentItems = currentStep === 'lab' ? labs : departments;
    const itemHeight = 50; // Approximate height of each item
    const newPosition = Math.floor(draggedItemIndex + event.nativeEvent.translationY / itemHeight);
    
    if (newPosition >= 0 && newPosition < currentItems.length && newPosition !== draggedItemIndex) {
      // Reorder the items
      const updatedItems = [...currentItems];
      const [movedItem] = updatedItems.splice(draggedItemIndex, 1);
      updatedItems.splice(newPosition, 0, movedItem);
      
      if (currentStep === 'lab') {
        setLabs(updatedItems);
      } else {
        setDepartments(updatedItems);
      }
      
      setDraggedItemIndex(newPosition);
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    }
  };

  // Handle end of drag
  const onPanGestureEnd = () => {
    setIsDragging(false);
    setDraggedItem(null);
    dragY.setValue(0);
  };

  // Render an item (lab or department)
  const renderItem = (item, index, isSelected) => {
    const isDraggingThis = isDragging && draggedItem && draggedItem._id === item._id;

    // For labs, check if it's completed (has departments selected)
    const isCompleted = currentStep === 'lab' && completedLabDepartments[item._id] && completedLabDepartments[item._id].length > 0;

    const itemStyle = [
      styles.option,
      isSelected && styles.selectedItem,
      isCompleted && styles.completedItem,
      isDraggingThis && styles.draggingItem
    ];
    
    // Create a dynamic animated style for the dragged item
    const animatedStyle = isDraggingThis ? {
      transform: [{ translateY: dragY }],
      zIndex: 100,
      shadowColor: "#000",
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.25,
      shadowRadius: 3.84,
      elevation: 5,
    } : {};
    
    return (
      <View key={item._id} style={styles.optionRow}>
        <Text style={styles.indexText}>{index + 1}.</Text>

        <Animated.View
          style={[itemStyle, animatedStyle]}
          ref={(ref) => {
            if (ref) {
              itemRefs.current[item._id] = ref;
            }
          }}
        >
          <TouchableOpacity
            style={styles.itemContent}
            onPress={() => currentStep === 'lab' ? handleLabSelect(item) : handleDepartmentSelect(item)}
          >
            <Text style={[
              styles.optionText,
              isSelected && styles.selectedText
            ]}>{item.name}</Text>

            {/* Show department count for completed labs */}
            {currentStep === 'lab' && isCompleted && (
              <Text style={styles.departmentCount}>
                ({completedLabDepartments[item._id].length} dept{completedLabDepartments[item._id].length > 1 ? 's' : ''})
              </Text>
            )}
          </TouchableOpacity>

          <PanGestureHandler
            onGestureEvent={onPanGestureEvent}
            onEnded={onPanGestureEnd}
            onBegan={() => onDragStart(item, index)}
          >
            <View style={styles.dragHandle}>
              <Text style={styles.dragHandleText}>≡</Text>
            </View>
          </PanGestureHandler>
        </Animated.View>
      </View>
    );
  };

  return (
    <Modal visible={isVisible} animationType="fade" transparent={true}>
      <GestureHandlerRootView style={{ flex: 1 }}>
        <View style={styles.overlay}>
          <View style={styles.modalView}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>
                {currentStep === 'lab' 
                  ? 'Select Lab' 
                  : `Select Department for ${selectedLab ? selectedLab.name : ''}`}
              </Text>
            </View>

            {isLoading ? (
              <View style={styles.loaderContainer}>
                <ActivityIndicator size="large" color="#007BFF" />
                <Text style={styles.loaderText}>
                  {currentStep === 'lab' ? 'Loading labs...' : 'Loading departments...'}
                </Text>
              </View>
            ) : currentStep === 'lab' ? (
              // Lab Selection View
              labs.length > 0 ? (
                <View style={styles.optionsContainer}>
                  {labs.map((lab, index) =>
                    renderItem(lab, index, completedLabDepartments[lab._id] && completedLabDepartments[lab._id].length > 0)
                  )}
                </View>
              ) : (
                <Text style={styles.noDataText}>
                  {errors.labsFetch ? 'Error loading labs' : 'No labs available'}
                </Text>
              )
            ) : (
              // Department Selection View
              departments.length > 0 ? (
                <View style={styles.optionsContainer}>
                  {departments.map((dept, index) =>
                    renderItem(dept, index, selectedDepartments.includes(dept._id))
                  )}
                </View>
              ) : (
                <Text style={styles.noDataText}>
                  {errors.departmentsFetch ? 'Error loading departments' : 'No departments available'}
                </Text>
              )
            )}

            <View style={styles.buttonRow}>
              {currentStep === 'lab' ? (
                <>
                  <TouchableOpacity
                    style={[styles.button, styles.secondaryButton]}
                    onPress={onClose}
                  >
                    <Text style={[styles.buttonText, styles.secondaryButtonText]}>Cancel</Text>
                  </TouchableOpacity>
                  <View style={styles.buttonDivider} />
                  <TouchableOpacity
                    style={[
                      styles.button,
                      styles.primaryButton,
                      !hasCompletedSelections && styles.disabledButton
                    ]}
                    onPress={handleFinalSubmit}
                    disabled={!hasCompletedSelections}
                  >
                    <Text style={[styles.buttonText, styles.primaryButtonText]}>Submit</Text>
                  </TouchableOpacity>
                </>
              ) : (
                <>
                  <TouchableOpacity
                    style={[styles.button, styles.backButton]}
                    onPress={goBackToLabs}
                  >
                    <Text style={[styles.buttonText]}>Back</Text>
                  </TouchableOpacity>
                  <View style={styles.buttonDivider} />
                  <TouchableOpacity
                    style={[
                      styles.button,
                      styles.primaryButton,
                      (selectedDepartments.length === 0 || isDragging) && styles.disabledButton
                    ]}
                    onPress={handleConfirm}
                    disabled={selectedDepartments.length === 0 || isDragging}
                  >
                    <Text style={[styles.buttonText, styles.primaryButtonText]}>Confirm</Text>
                  </TouchableOpacity>
                </>
              )}
            </View>
          </View>
        </View>
      </GestureHandlerRootView>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "rgba(0, 0, 0, 0.7)",
  },
  modalView: {
    width: "80%",
    backgroundColor: Colors.background,
    borderRadius: 10,
    padding: 20,
    maxHeight: '80%',
  },
  modalHeader: {
    marginBottom: 15,
    paddingBottom: 10,
    flexDirection: 'row',
    justifyContent: 'flex-start',
    position: 'relative',
  },
  modalTitle: {
    fontSize: 18,
    fontFamily: 'Roboto_bold',
    textAlign: 'left',
    color: Colors.black,
  },
  optionsContainer: {
    maxHeight: 300,
    paddingHorizontal: 10,
  },
  option: {
    flex: 1,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: Colors.border,
    backgroundColor: Colors.background,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
    flexDirection: 'row',
    alignItems: 'center',
  },
  selectedItem: {
    backgroundColor: '#E8F0FE',
    borderColor: Colors.primary,
    borderWidth: 2,
  },
  completedItem: {
    backgroundColor: '#E8F0FE',
    borderColor: Colors.primary,
    borderWidth: 2,
  },
  optionRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  indexText: {
    color: 'black',
    fontFamily: 'Roboto_bold',
    fontSize: 18,
    width: 30,
    textAlign: 'center',
    marginRight: 10,
  },
  itemContent: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    padding: 10,
  },
  optionText: {
    fontSize: 16,
    fontFamily: 'Roboto',
    color: Colors.black,
    textAlign: 'left',
  },
  selectedText: {
    fontFamily: 'Roboto_bold',
    color: Colors.primary,
  },
  departmentCount: {
    fontSize: 12,
    fontFamily: 'Roboto',
    color: Colors.primary,
    marginLeft: 8,
    fontStyle: 'italic',
  },
  dragHandle: {
    width: 30,
    height: 30,
    justifyContent: 'center',
    alignItems: 'center',
    borderLeftWidth: 2,
    borderLeftColor: Colors.border,
    marginLeft: 5,
  },
  dragHandleText: {
    fontSize: 24,
    color: Colors.lightText,
  },
  noDataText: {
    padding: 20,
    textAlign: 'center',
    color: Colors.lightText,
    fontFamily: 'Roboto',
  },
  buttonRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginTop: 20,
  },
  button: {
    padding: 12,
    borderRadius: 30,
    flex: 1,
    alignItems: "center",
  },
  primaryButton: {
    backgroundColor: Colors.primary,
  },
  secondaryButton: {
    backgroundColor: Colors.lightText,
  },
  backButton: {
    backgroundColor: Colors.lightText,
  },
  disabledButton: {
    backgroundColor: Colors.disabled,
  },
  buttonText: {
    color: Colors.background,
    fontFamily: 'Roboto_bold',
  },
  secondaryButtonText: {
    color: "white",
  },
  primaryButtonText: {
    color: "white",
  },
  buttonDivider: {
    width: 10,
  },
  loaderContainer: {
    padding: 30,
    alignItems: 'center',
    justifyContent: 'center',
  },
  loaderText: {
    marginTop: 10,
    color: Colors.lightText,
    fontSize: 16,
    fontFamily: 'Roboto',
  },
  draggingItem: {
    backgroundColor: "#F5F5F5",
  },
});

export default LabSelectionModal;